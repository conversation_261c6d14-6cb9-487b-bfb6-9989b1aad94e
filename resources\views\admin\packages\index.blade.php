@extends('layouts.admin')

@section('title', 'จัดการแพคเกจ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">จัดการแพคเกจ</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">จัดการแพคเกจ</h1>
    <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>เพิ่มแพคเกจใหม่
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($packages->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>รูปภาพ</th>
                        <th>ชื่อแพคเกจ</th>
                        <th>คำอธิบาย</th>
                        <th>ราคา</th>
                        <th>ระยะเวลา</th>
                        <th>สถานะ</th>
                        <th>แนะนำ</th>
                        <th>ลำดับ</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($packages as $package)
                    <tr>
                        <td>
                            @if($package->image)
                            <img src="{{ asset('storage/' . $package->image) }}" alt="{{ $package->name }}" 
                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                            @else
                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-box text-muted"></i>
                            </div>
                            @endif
                        </td>
                        <td>
                            <strong>{{ $package->name }}</strong>
                        </td>
                        <td>
                            {{ Str::limit($package->description, 50) }}
                        </td>
                        <td>
                            <span class="text-success fw-bold">{{ number_format($package->price, 0) }} บาท</span>
                        </td>
                        <td>
                            @if($package->duration)
                            <span class="badge bg-info">{{ $package->duration }}</span>
                            @else
                            <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            @if($package->is_active)
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            @else
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            @endif
                        </td>
                        <td>
                            @if($package->is_featured)
                            <span class="badge bg-warning text-dark">แนะนำ</span>
                            @else
                            <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $package->sort_order }}</span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.packages.edit', $package->id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.packages.delete', $package->id) }}" 
                                      method="POST" class="d-inline"
                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบแพคเกจนี้?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-box fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">ยังไม่มีแพคเกจ</h4>
            <p class="text-muted">เริ่มต้นด้วยการเพิ่มแพคเกจแรกของคุณ</p>
            <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>เพิ่มแพคเกจใหม่
            </a>
        </div>
        @endif
    </div>
</div>
@endsection
