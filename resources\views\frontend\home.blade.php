@extends('layouts.app')

@section('title', 'หน้าหลัก - ' . ($settings['site_name'] ?? 'บริษัทของเรา'))

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">{{ $settings['site_name'] ?? 'บริษัทของเรา' }}</h1>
                <p class="lead mb-4">{{ $settings['site_description'] ?? 'บริการที่ดีที่สุดสำหรับคุณ' }}</p>
                <div class="d-flex gap-3">
                    <a href="{{ route('services') }}" class="btn btn-light btn-lg">ดูบริการของเรา</a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">ติดต่อเรา</a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-rocket fa-10x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
@if($services->count() > 0)
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">บริการของเรา</h2>
            <p class="text-muted">บริการที่หลากหลายเพื่อตอบสนองความต้องการของคุณ</p>
        </div>
        
        <div class="row g-4">
            @foreach($services as $service)
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    @if($service->image)
                    <img src="{{ asset('storage/' . $service->image) }}" class="card-img-top" alt="{{ $service->title }}" style="height: 200px; object-fit: cover;">
                    @else
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                    @endif
                    
                    <div class="card-body">
                        <h5 class="card-title">{{ $service->title }}</h5>
                        <p class="card-text">{{ Str::limit($service->description, 100) }}</p>
                        @if($service->price)
                        <p class="text-primary fw-bold">ราคา: {{ number_format($service->price, 0) }} บาท</p>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-5">
            <a href="{{ route('services') }}" class="btn btn-primary btn-lg">ดูบริการทั้งหมด</a>
        </div>
    </div>
</section>
@endif

<!-- Packages Section -->
@if($packages->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">แพคเกจแนะนำ</h2>
            <p class="text-muted">แพคเกจที่คุ้มค่าและตอบโจทย์ความต้องการของคุณ</p>
        </div>
        
        <div class="row g-4">
            @foreach($packages as $package)
            <div class="col-md-6 col-lg-4">
                <div class="card package-card h-100">
                    @if($package->image)
                    <img src="{{ asset('storage/' . $package->image) }}" class="card-img-top" alt="{{ $package->name }}" style="height: 200px; object-fit: cover;">
                    @else
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-box fa-3x text-muted"></i>
                    </div>
                    @endif
                    
                    <div class="card-body">
                        <h5 class="card-title">
                            {{ $package->name }}
                            @if($package->is_featured)
                            <span class="badge bg-warning text-dark">แนะนำ</span>
                            @endif
                        </h5>
                        <p class="card-text">{{ Str::limit($package->description, 100) }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="h5 text-primary mb-0">{{ number_format($package->price, 0) }} บาท</span>
                            @if($package->duration)
                            <small class="text-muted">{{ $package->duration }}</small>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-5">
            <a href="{{ route('packages') }}" class="btn btn-primary btn-lg">ดูแพคเกจทั้งหมด</a>
        </div>
    </div>
</section>
@endif

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">พร้อมเริ่มต้นแล้วหรือยัง?</h2>
                <p class="lead mb-4">ติดต่อเราวันนี้เพื่อปรึกษาและรับคำแนะนำฟรี</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">ติดต่อเรา</a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
