<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\Package;
use App\Models\Contact;
use App\Models\SiteSetting;

class HomeController extends Controller
{
    public function index()
    {
        $services = Service::active()->ordered()->take(6)->get();
        $packages = Package::active()->featured()->ordered()->take(3)->get();
        $settings = $this->getSettings();

        return view('frontend.home', compact('services', 'packages', 'settings'));
    }

    public function services()
    {
        $services = Service::active()->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.services', compact('services', 'settings'));
    }

    public function packages()
    {
        $packages = Package::active()->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.packages', compact('packages', 'settings'));
    }

    public function contact()
    {
        $settings = $this->getSettings();

        return view('frontend.contact', compact('settings'));
    }

    public function storeContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string'
        ]);

        Contact::create($request->all());

        return redirect()->route('contact')->with('success', 'ข้อความของคุณถูกส่งเรียบร้อยแล้ว เราจะติดต่อกลับโดยเร็วที่สุด');
    }

    private function getSettings()
    {
        return [
            'site_name' => SiteSetting::get('site_name', 'บริษัทของเรา'),
            'site_description' => SiteSetting::get('site_description', 'บริการที่ดีที่สุดสำหรับคุณ'),
            'contact_phone' => SiteSetting::get('contact_phone', '02-xxx-xxxx'),
            'contact_email' => SiteSetting::get('contact_email', '<EMAIL>'),
            'contact_address' => SiteSetting::get('contact_address', 'ที่อยู่บริษัท'),
            'facebook_url' => SiteSetting::get('facebook_url', ''),
            'line_id' => SiteSetting::get('line_id', ''),
        ];
    }
}
