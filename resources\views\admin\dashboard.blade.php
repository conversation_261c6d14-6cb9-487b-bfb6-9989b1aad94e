@extends('layouts.admin')

@section('title', 'แดshboard - ระบบจัดการ')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">แดshboard</h1>
    <div class="text-muted">
        <i class="fas fa-calendar me-2"></i>{{ date('d/m/Y H:i') }}
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">บริการทั้งหมด</h6>
                        <h2 class="mb-0">{{ $stats['services_count'] }}</h2>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">แพคเกจทั้งหมด</h6>
                        <h2 class="mb-0">{{ $stats['packages_count'] }}</h2>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">ข้อความติดต่อ</h6>
                        <h2 class="mb-0">{{ $stats['contacts_count'] }}</h2>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-envelope fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">ข้อความใหม่</h6>
                        <h2 class="mb-0">{{ $stats['unread_contacts'] }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-bell fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">การดำเนินการด่วน</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                    </a>
                    <a href="{{ route('admin.packages.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>เพิ่มแพคเกจใหม่
                    </a>
                    <a href="{{ route('admin.settings') }}" class="btn btn-info">
                        <i class="fas fa-cog me-2"></i>ตั้งค่าเว็บไซต์
                    </a>
                    <a href="{{ route('home') }}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">ข้อความติดต่อล่าสุด</h5>
                <a href="{{ route('admin.contacts') }}" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
            </div>
            <div class="card-body">
                @if($recent_contacts->count() > 0)
                <div class="list-group list-group-flush">
                    @foreach($recent_contacts as $contact)
                    <div class="list-group-item px-0 {{ !$contact->is_read ? 'bg-light' : '' }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    {{ $contact->name }}
                                    @if(!$contact->is_read)
                                    <span class="badge bg-warning text-dark">ใหม่</span>
                                    @endif
                                </h6>
                                <p class="mb-1 text-muted small">{{ $contact->subject }}</p>
                                <small class="text-muted">{{ $contact->created_at->diffForHumans() }}</small>
                            </div>
                            <a href="{{ route('admin.contacts.show', $contact->id) }}" class="btn btn-sm btn-outline-primary">ดู</a>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>ยังไม่มีข้อความติดต่อ</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- System Info -->
<div class="row g-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ข้อมูลระบบ</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>เวอร์ชัน Laravel:</strong></td>
                                <td>{{ app()->version() }}</td>
                            </tr>
                            <tr>
                                <td><strong>เวอร์ชัน PHP:</strong></td>
                                <td>{{ PHP_VERSION }}</td>
                            </tr>
                            <tr>
                                <td><strong>สถานะ:</strong></td>
                                <td><span class="badge bg-success">ออนไลน์</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>ผู้ใช้งาน:</strong></td>
                                <td>{{ Auth::user()->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>เข้าสู่ระบบล่าสุด:</strong></td>
                                <td>{{ now()->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <td><strong>สิทธิ์:</strong></td>
                                <td><span class="badge bg-primary">ผู้ดูแลระบบ</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
