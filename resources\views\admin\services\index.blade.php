@extends('layouts.admin')

@section('title', 'จัดการบริการ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">จัดการบริการ</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">จัดการบริการ</h1>
    <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($services->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>รูปภาพ</th>
                        <th>ชื่อบริการ</th>
                        <th>คำอธิบาย</th>
                        <th>ราคา</th>
                        <th>สถานะ</th>
                        <th>ลำดับ</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($services as $service)
                    <tr>
                        <td>
                            @if($service->image)
                            <img src="{{ asset('storage/' . $service->image) }}" alt="{{ $service->title }}" 
                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                            @else
                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            @endif
                        </td>
                        <td>
                            <strong>{{ $service->title }}</strong>
                        </td>
                        <td>
                            {{ Str::limit($service->description, 50) }}
                        </td>
                        <td>
                            @if($service->price)
                            <span class="text-success fw-bold">{{ number_format($service->price, 0) }} บาท</span>
                            @else
                            <span class="text-muted">ไม่ระบุ</span>
                            @endif
                        </td>
                        <td>
                            @if($service->is_active)
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            @else
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $service->sort_order }}</span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.services.edit', $service->id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.services.delete', $service->id) }}" 
                                      method="POST" class="d-inline"
                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-cogs fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">ยังไม่มีบริการ</h4>
            <p class="text-muted">เริ่มต้นด้วยการเพิ่มบริการแรกของคุณ</p>
            <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
            </a>
        </div>
        @endif
    </div>
</div>
@endsection
