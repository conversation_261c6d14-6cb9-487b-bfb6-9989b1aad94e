<?php $__env->startSection('title', 'จัดการบริการ - ระบบจัดการ'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item active">จัดการบริการ</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">จัดการบริการ</h1>
    <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
    </a>
</div>

<div class="card">
    <div class="card-body">
        <?php if($services->count() > 0): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>รูปภาพ</th>
                        <th>ชื่อบริการ</th>
                        <th>คำอธิบาย</th>
                        <th>ราคา</th>
                        <th>สถานะ</th>
                        <th>ลำดับ</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php if($service->image): ?>
                            <img src="<?php echo e(asset('storage/' . $service->image)); ?>" alt="<?php echo e($service->title); ?>" 
                                 class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                            <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?php echo e($service->title); ?></strong>
                        </td>
                        <td>
                            <?php echo e(Str::limit($service->description, 50)); ?>

                        </td>
                        <td>
                            <?php if($service->price): ?>
                            <span class="text-success fw-bold"><?php echo e(number_format($service->price, 0)); ?> บาท</span>
                            <?php else: ?>
                            <span class="text-muted">ไม่ระบุ</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($service->is_active): ?>
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            <?php else: ?>
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo e($service->sort_order); ?></span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('admin.services.edit', $service->id)); ?>" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('admin.services.delete', $service->id)); ?>" 
                                      method="POST" class="d-inline"
                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-cogs fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">ยังไม่มีบริการ</h4>
            <p class="text-muted">เริ่มต้นด้วยการเพิ่มบริการแรกของคุณ</p>
            <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/services/index.blade.php ENDPATH**/ ?>