<?php $__env->startSection('title', 'บริการ - ' . ($settings['site_name'] ?? 'บริษัทของเรา')); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<section class="hero-section">
    <div class="container">
        <div class="text-center">
            <h1 class="display-4 fw-bold mb-4">บริการของเรา</h1>
            <p class="lead">บริการที่หลากหลายและครบครันเพื่อตอบสนองความต้องการของคุณ</p>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-5">
    <div class="container">
        <?php if($services->count() > 0): ?>
        <div class="row g-4">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    <?php if($service->image): ?>
                    <img src="<?php echo e(asset('storage/' . $service->image)); ?>" class="card-img-top" alt="<?php echo e($service->title); ?>" style="height: 250px; object-fit: cover;">
                    <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                        <i class="fas fa-cogs fa-4x text-muted"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo e($service->title); ?></h5>
                        <p class="card-text flex-grow-1"><?php echo e($service->description); ?></p>
                        
                        <?php if($service->details): ?>
                        <div class="mb-3">
                            <h6>รายละเอียด:</h6>
                            <p class="small text-muted"><?php echo e(Str::limit($service->details, 150)); ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($service->price): ?>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 text-primary mb-0"><?php echo e(number_format($service->price, 0)); ?> บาท</span>
                                <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">สอบถาม</a>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="mt-auto">
                            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary w-100">สอบถามราคา</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-cogs fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีบริการ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามบริการ</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Contact CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">สนใจบริการของเรา?</h2>
                <p class="lead mb-4">ติดต่อเราเพื่อปรึกษาและขอใบเสนอราคา</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">ติดต่อเรา</a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                    <?php if(!empty($settings['line_id'])): ?>
                    <a href="https://line.me/ti/p/<?php echo e($settings['line_id']); ?>" class="btn btn-success btn-lg" target="_blank">
                        <i class="fab fa-line me-2"></i>Line
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/frontend/services.blade.php ENDPATH**/ ?>