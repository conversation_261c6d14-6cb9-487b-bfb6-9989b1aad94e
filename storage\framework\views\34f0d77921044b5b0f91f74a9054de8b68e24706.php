<?php $__env->startSection('title', 'หน้าหลัก - ' . ($settings['site_name'] ?? 'บริษัทของเรา')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4"><?php echo e($settings['site_name'] ?? 'บริษัทของเรา'); ?></h1>
                <p class="lead mb-4"><?php echo e($settings['site_description'] ?? 'บริการที่ดีที่สุดสำหรับคุณ'); ?></p>
                <div class="d-flex gap-3">
                    <a href="<?php echo e(route('services')); ?>" class="btn btn-light btn-lg">ดูบริการของเรา</a>
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-light btn-lg">ติดต่อเรา</a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-rocket fa-10x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<?php if($services->count() > 0): ?>
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">บริการของเรา</h2>
            <p class="text-muted">บริการที่หลากหลายเพื่อตอบสนองความต้องการของคุณ</p>
        </div>
        
        <div class="row g-4">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    <?php if($service->image): ?>
                    <img src="<?php echo e(asset('storage/' . $service->image)); ?>" class="card-img-top" alt="<?php echo e($service->title); ?>" style="height: 200px; object-fit: cover;">
                    <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <h5 class="card-title"><?php echo e($service->title); ?></h5>
                        <p class="card-text"><?php echo e(Str::limit($service->description, 100)); ?></p>
                        <?php if($service->price): ?>
                        <p class="text-primary fw-bold">ราคา: <?php echo e(number_format($service->price, 0)); ?> บาท</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-5">
            <a href="<?php echo e(route('services')); ?>" class="btn btn-primary btn-lg">ดูบริการทั้งหมด</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Packages Section -->
<?php if($packages->count() > 0): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">แพคเกจแนะนำ</h2>
            <p class="text-muted">แพคเกจที่คุ้มค่าและตอบโจทย์ความต้องการของคุณ</p>
        </div>
        
        <div class="row g-4">
            <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card package-card h-100">
                    <?php if($package->image): ?>
                    <img src="<?php echo e(asset('storage/' . $package->image)); ?>" class="card-img-top" alt="<?php echo e($package->name); ?>" style="height: 200px; object-fit: cover;">
                    <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-box fa-3x text-muted"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <h5 class="card-title">
                            <?php echo e($package->name); ?>

                            <?php if($package->is_featured): ?>
                            <span class="badge bg-warning text-dark">แนะนำ</span>
                            <?php endif; ?>
                        </h5>
                        <p class="card-text"><?php echo e(Str::limit($package->description, 100)); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="h5 text-primary mb-0"><?php echo e(number_format($package->price, 0)); ?> บาท</span>
                            <?php if($package->duration): ?>
                            <small class="text-muted"><?php echo e($package->duration); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-5">
            <a href="<?php echo e(route('packages')); ?>" class="btn btn-primary btn-lg">ดูแพคเกจทั้งหมด</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">พร้อมเริ่มต้นแล้วหรือยัง?</h2>
                <p class="lead mb-4">ติดต่อเราวันนี้เพื่อปรึกษาและรับคำแนะนำฟรี</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">ติดต่อเรา</a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/frontend/home.blade.php ENDPATH**/ ?>